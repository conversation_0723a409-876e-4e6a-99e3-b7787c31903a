<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Test - mermantic</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    button { padding: 10px 15px; margin: 5px; }
    .result { margin-top: 10px; padding: 10px; background: #f5f5f5; }
    .error { background: #ffebee; color: #c62828; }
    .success { background: #e8f5e8; color: #2e7d32; }
  </style>
</head>
<body>
  <h1>API Test Page</h1>

  <div class="test-section">
    <h3>Test Basic API Connection</h3>
    <button onclick="testBasicAPI()">Test /api/users/me</button>
    <div id="basicResult" class="result"></div>
  </div>

  <div class="test-section">
    <h3>Test Password Reset Request</h3>
    <input type="email" id="resetEmail" placeholder="Enter email" value="<EMAIL>">
    <button onclick="testPasswordReset()">Test Password Reset</button>
    <div id="resetResult" class="result"></div>
  </div>

  <div class="test-section">
    <h3>Test User Registration (with Welcome Email)</h3>
    <input type="text" id="regUsername" placeholder="Username" value="testuser123">
    <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>">
    <input type="password" id="regPassword" placeholder="Password" value="password123">
    <button onclick="testRegistration()">Test Registration</button>
    <div id="regResult" class="result"></div>
  </div>

  <script>
    async function testBasicAPI() {
      const resultDiv = document.getElementById('basicResult');

      try {
        const response = await fetch('/api/users/me', {
          method: 'GET'
        });

        const data = await response.json();

        if (response.status === 401) {
          resultDiv.className = 'result success';
          resultDiv.textContent = `Success: API is working (${data.message})`;
        } else {
          resultDiv.className = 'result error';
          resultDiv.textContent = `Unexpected response: ${data.message}`;
        }
      } catch (error) {
        resultDiv.className = 'result error';
        resultDiv.textContent = `Network Error: ${error.message}`;
      }
    }

    async function testPasswordReset() {
      const email = document.getElementById('resetEmail').value;
      const resultDiv = document.getElementById('resetResult');

      try {
        const response = await fetch('/api/users/forgot-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email })
        });

        const data = await response.json();

        if (response.ok) {
          resultDiv.className = 'result success';
          resultDiv.textContent = `Success: ${data.message}`;
        } else {
          resultDiv.className = 'result error';
          resultDiv.textContent = `Error: ${data.message}`;
        }
      } catch (error) {
        resultDiv.className = 'result error';
        resultDiv.textContent = `Network Error: ${error.message}`;
      }
    }

    async function testRegistration() {
      const username = document.getElementById('regUsername').value;
      const email = document.getElementById('regEmail').value;
      const password = document.getElementById('regPassword').value;
      const resultDiv = document.getElementById('regResult');

      try {
        const response = await fetch('/api/users/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username, email, password })
        });

        const data = await response.json();

        if (response.ok) {
          resultDiv.className = 'result success';
          resultDiv.textContent = `Success: ${data.message} (Welcome email should be sent)`;
        } else {
          resultDiv.className = 'result error';
          resultDiv.textContent = `Error: ${data.message}`;
        }
      } catch (error) {
        resultDiv.className = 'result error';
        resultDiv.textContent = `Network Error: ${error.message}`;
      }
    }
  </script>
</body>
</html>
