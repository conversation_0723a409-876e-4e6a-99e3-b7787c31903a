const { Resend } = require('resend');

class EmailService {
  static getResendClient() {
    if (!this.resend) {
      this.resend = new Resend(process.env.RESEND_API_KEY);
    }
    return this.resend;
  }
  static async sendWelcomeEmail(userEmail, username) {
    try {
      const resend = this.getResendClient();
      const { data, error } = await resend.emails.send({
        from: process.env.FROM_EMAIL,
        to: [userEmail],
        subject: `Welcome to ${process.env.APP_NAME}!`,
        html: this.getWelcomeEmailTemplate(username),
      });

      if (error) {
        console.error('Error sending welcome email:', error);
        throw error;
      }

      console.log('Welcome email sent successfully:', data);
      return data;
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      throw error;
    }
  }

  static async sendPasswordResetEmail(userEmail, username, resetToken) {
    try {
      const resetUrl = `${process.env.APP_URL}/reset-password.html?token=${resetToken}`;
      const resend = this.getResendClient();

      const { data, error } = await resend.emails.send({
        from: process.env.FROM_EMAIL,
        to: [userEmail],
        subject: `Reset your ${process.env.APP_NAME} password`,
        html: this.getPasswordResetEmailTemplate(username, resetUrl),
      });

      if (error) {
        console.error('Error sending password reset email:', error);
        throw error;
      }

      console.log('Password reset email sent successfully:', data);
      return data;
    } catch (error) {
      console.error('Failed to send password reset email:', error);
      throw error;
    }
  }

  static getWelcomeEmailTemplate(username) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to ${process.env.APP_NAME}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #f0f0f0;
          }
          .logo {
            font-size: 28px;
            font-weight: bold;
            color: #2563eb;
          }
          .content {
            padding: 30px 0;
          }
          .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
          }
          .footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #f0f0f0;
            color: #666;
            font-size: 14px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="logo">${process.env.APP_NAME}</div>
        </div>

        <div class="content">
          <h1>Welcome to ${process.env.APP_NAME}, ${username}!</h1>

          <p>Thank you for joining our community of diagram creators! We're excited to have you on board.</p>

          <p>With ${process.env.APP_NAME}, you can:</p>
          <ul>
            <li>Create beautiful Mermaid diagrams with ease</li>
            <li>Save and organize your diagrams</li>
            <li>Share your diagrams with others</li>
            <li>Access your work from anywhere</li>
          </ul>

          <p>Ready to get started?</p>

          <a href="${process.env.APP_URL}/dashboard.html" class="button">Start Creating Diagrams</a>

          <p>If you have any questions or need help getting started, feel free to reach out to our support team.</p>

          <p>Happy diagramming!</p>
          <p>The ${process.env.APP_NAME} Team</p>
        </div>

        <div class="footer">
          <p>&copy; 2023 ${process.env.APP_NAME}. All rights reserved.</p>
          <p>You received this email because you created an account on ${process.env.APP_NAME}.</p>
        </div>
      </body>
      </html>
    `;
  }

  static getPasswordResetEmailTemplate(username, resetUrl) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #f0f0f0;
          }
          .logo {
            font-size: 28px;
            font-weight: bold;
            color: #2563eb;
          }
          .content {
            padding: 30px 0;
          }
          .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #dc2626;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
          }
          .footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #f0f0f0;
            color: #666;
            font-size: 14px;
          }
          .warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="logo">${process.env.APP_NAME}</div>
        </div>

        <div class="content">
          <h1>Reset Your Password</h1>

          <p>Hi ${username},</p>

          <p>We received a request to reset your password for your ${process.env.APP_NAME} account.</p>

          <p>Click the button below to reset your password:</p>

          <a href="${resetUrl}" class="button">Reset Password</a>

          <div class="warning">
            <strong>Important:</strong> This link will expire in 1 hour for security reasons.
          </div>

          <p>If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.</p>

          <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>

          <p>Best regards,<br>The ${process.env.APP_NAME} Team</p>
        </div>

        <div class="footer">
          <p>&copy; 2023 ${process.env.APP_NAME}. All rights reserved.</p>
          <p>You received this email because a password reset was requested for your account.</p>
        </div>
      </body>
      </html>
    `;
  }
}

module.exports = EmailService;
