<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register - mermantic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <!-- Global Navigation Container -->
  <div id="navigation-container"></div>

  <main class="container">
    <section class="auth-form">
      <h2>Create an Account</h2>
      <div id="error-message" class="error-message"></div>

      <form id="register-form">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" id="username" name="username" class="nui-input" required>
        </div>

        <div class="form-group">
          <label for="email">Email</label>
          <input type="email" id="email" name="email" class="nui-input" required>
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" class="nui-input" required>
        </div>

        <div class="form-group">
          <label for="confirm-password">Confirm Password</label>
          <input type="password" id="confirm-password" name="confirm-password" class="nui-input" required>
        </div>

        <div class="form-actions">
          <button type="submit" class="nui-button primary">Register</button>
        </div>
      </form>

      <div class="social-login">
        <p>Or register with:</p>
        <a href="/api/auth/google" class="nui-button google-btn">
          <img src="/img/google-icon.svg" alt="Google" width="20" height="20">
          Register with Google
        </a>
      </div>

      <p class="auth-redirect">
        Already have an account? <a href="/login.html">Login here</a>
      </p>
    </section>
  </main>

  <!-- Global Footer Container -->
  <div id="footer-container"></div>

  <!-- Component Scripts -->
  <script src="/js/components/navigation.js"></script>
  <script src="/js/components/footer.js"></script>
  <script src="/js/components/component-loader.js"></script>

  <script src="/nui/nui.js"></script>
  <script src="/js/auth.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const registerForm = document.getElementById('register-form');
      const errorMessage = document.getElementById('error-message');

      registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        // Validate passwords match
        if (password !== confirmPassword) {
          errorMessage.textContent = 'Passwords do not match';
          errorMessage.style.display = 'block';
          return;
        }

        try {
          const response = await fetch('/api/users/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, email, password })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.message || 'Registration failed');
          }

          // Redirect to login page
          window.location.href = '/login.html?registered=true';
        } catch (error) {
          errorMessage.textContent = error.message;
          errorMessage.style.display = 'block';
        }
      });
    });
  </script>
</body>
</html>
