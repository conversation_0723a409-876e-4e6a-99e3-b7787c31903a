require('dotenv').config();
const express = require('express');
const session = require('express-session');
const cors = require('cors');
const path = require('path');
const passport = require('passport');

// Import routes
const userRoutes = require('./routes/users');
const chartRoutes = require('./routes/charts');
const authRoutes = require('./routes/auth');
const renderRoutes = require('./routes/render');

// Import passport configuration
require('./config/passport');

// Initialize database
const db = require('./db');

// Import database debugging utility
const { checkDatabaseSchema } = require('./utils/db-debug');

const app = express();
const PORT = process.env.PORT || 3000;

// Determine allowed origins based on environment
const getAllowedOrigins = () => {
  if (process.env.NODE_ENV === 'production') {
    return ['https://mermantic.net', 'https://www.mermantic.net'];
  } else {
    return ['http://localhost:3000', 'http://127.0.0.1:3000'];
  }
};

// Middleware
app.use(cors({
  origin: getAllowedOrigins(),
  credentials: true // Allow cookies to be sent
}));

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// JSON parsing with error handling
app.use(express.json({
  verify: (req, res, buf, encoding) => {
    try {
      JSON.parse(buf);
    } catch (e) {
      console.error('JSON Parse Error:', e.message);
      console.error('Raw body:', buf.toString());
      res.status(400).json({ message: 'Invalid JSON format' });
      return;
    }
  }
}));
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));
app.use(express.static(path.join(__dirname, '../frontend')));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'default_secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    domain: process.env.NODE_ENV === 'production' ? '.mermantic.net' : undefined
  },
  name: 'mermantic.sid' // Custom session name
}));

// Initialize passport
app.use(passport.initialize());
app.use(passport.session());

// Routes
app.use('/api/users', userRoutes);
app.use('/api/charts', chartRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/render', renderRoutes);

// Serve frontend
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);

  // Check database schema on startup
  checkDatabaseSchema();

  // Log environment variables (without sensitive values)
  console.log('Environment variables:');
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('PORT:', process.env.PORT);
  console.log('GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Not set');
  console.log('GOOGLE_CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not set');

  // Show the callback URL being used
  const callbackURL = process.env.NODE_ENV === 'production'
    ? 'https://mermantic.net/api/auth/google/callback'
    : 'http://localhost:3000/api/auth/google/callback';
  console.log('Google Callback URL:', callbackURL);
  console.log('Allowed CORS Origins:', getAllowedOrigins());
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err);
});
