// Component Loader - Initializes global components
class ComponentLoader {
  constructor() {
    this.navigationComponent = null;
    this.footerComponent = null;
  }

  // Initialize all components
  init() {
    this.initNavigation();
    this.initFooter();
    this.setupAuthStateListener();
  }

  initNavigation() {
    const navContainer = document.getElementById('navigation-container');
    if (navContainer) {
      this.navigationComponent = new NavigationComponent();
      this.navigationComponent.render();
    }
  }

  initFooter() {
    const footerContainer = document.getElementById('footer-container');
    if (footerContainer) {
      this.footerComponent = new FooterComponent();
      this.footerComponent.render();
    }
  }

  // Listen for auth state changes and update navigation
  setupAuthStateListener() {
    // Listen for storage changes (when user logs in/out in another tab)
    window.addEventListener('storage', (e) => {
      if (e.key === 'user') {
        this.updateNavigation();
      }
    });

    // Listen for custom auth events
    window.addEventListener('authStateChanged', () => {
      this.updateNavigation();
    });
  }

  updateNavigation() {
    if (this.navigationComponent) {
      this.navigationComponent.updateAuthState();
    }
  }

  // Static method to trigger auth state change event
  static triggerAuthStateChange() {
    window.dispatchEvent(new CustomEvent('authStateChanged'));
  }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Only initialize if components are available
  if (typeof NavigationComponent !== 'undefined' && typeof FooterComponent !== 'undefined') {
    window.componentLoader = new ComponentLoader();
    window.componentLoader.init();
  }
});

// Export for use in other scripts
window.ComponentLoader = ComponentLoader;
