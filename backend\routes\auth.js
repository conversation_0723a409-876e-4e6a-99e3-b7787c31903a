const express = require('express');
const passport = require('passport');
const router = express.Router();

// Test endpoint to verify callback URL is reachable
router.get('/google/callback/test', (req, res) => {
  res.json({
    message: 'Google callback URL is reachable',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    url: req.protocol + '://' + req.get('host') + req.originalUrl
  });
});

// Google OAuth login route
router.get('/google', (req, res, next) => {
  console.log('Google authentication route accessed');

  // Check if Google OAuth is properly configured
  if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
    console.error('Google OAuth not configured. Missing GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET');
    return res.redirect('/login.html?error=google-not-configured');
  }

  next();
}, passport.authenticate('google', { scope: ['profile', 'email'] }));

// Google OAuth callback route
router.get('/google/callback',
  (req, res, next) => {
    console.log('Google callback route accessed');
    console.log('Full URL:', req.protocol + '://' + req.get('host') + req.originalUrl);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Query parameters:', req.query);
    console.log('Session ID:', req.sessionID);
    console.log('Session data:', req.session);

    // Check if there's an error in the query parameters
    if (req.query.error) {
      console.error('Error in Google callback:', req.query.error);
      return res.redirect('/login.html?error=' + req.query.error);
    }

    next();
  },
  (req, res, next) => {
    console.log('About to authenticate with passport');
    // Wrap passport.authenticate in a try-catch to catch any errors
    try {
      passport.authenticate('google', {
        failureRedirect: '/login.html?error=google-auth-failed'
      })(req, res, next);
    } catch (error) {
      console.error('Error during passport authentication:', error);
      return res.redirect('/login.html?error=passport-error');
    }
  },
  (req, res) => {
    console.log('Google authentication successful');
    console.log('User:', req.user);
    console.log('Session before setting user:', req.session);

    // Successful authentication
    // Set user in session
    req.session.user = {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email,
      profile_picture: req.user.profile_picture,
      auth_type: req.user.auth_type
    };

    console.log('Session user set:', req.session.user);
    console.log('Session after setting user:', req.session);

    // Save session explicitly
    req.session.save((err) => {
      if (err) {
        console.error('Error saving session:', err);
        return res.redirect('/login.html?error=session-save-failed');
      }
      console.log('Session saved successfully, redirecting to dashboard');
      // Redirect to dashboard
      res.redirect('/dashboard.html');
    });
  }
);

module.exports = router;
