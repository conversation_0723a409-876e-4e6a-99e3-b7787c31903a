<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - mermantic</title>
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/nui/nui.css">
</head>
<body>
  <!-- Global Navigation Container -->
  <div id="navigation-container"></div>

  <main class="container">
    <section class="auth-form">
      <h2>Login to Your Account</h2>
      <div id="error-message" class="error-message"></div>

      <form id="login-form">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" id="username" name="username" class="nui-input" required>
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" class="nui-input" required>
        </div>

        <div class="form-actions">
          <button type="submit" class="nui-button primary">Login</button>
        </div>

        <div class="forgot-password">
          <a href="/forgot-password.html">Forgot your password?</a>
        </div>
      </form>

      <div class="social-login">
        <p>Or login with:</p>
        <a href="/api/auth/google" class="nui-button google-btn">
          <img src="/img/google-icon.svg" alt="Google" width="20" height="20">
          Login with Google
        </a>
      </div>

      <p class="auth-redirect">
        Don't have an account? <a href="/register.html">Register here</a>
      </p>
    </section>
  </main>

  <!-- Global Footer Container -->
  <div id="footer-container"></div>

  <!-- Component Scripts -->
  <script src="/js/components/navigation.js"></script>
  <script src="/js/components/footer.js"></script>
  <script src="/js/components/component-loader.js"></script>

  <script src="/nui/nui.js"></script>
  <script src="/js/auth.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const loginForm = document.getElementById('login-form');
      const errorMessage = document.getElementById('error-message');

      // Check for error messages in URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const error = urlParams.get('error');

      if (error) {
        let errorText = 'An error occurred during authentication.';

        switch (error) {
          case 'google-not-configured':
            errorText = 'Google authentication is not properly configured. Please contact the administrator.';
            break;
          case 'google-auth-failed':
            errorText = 'Google authentication failed. Please try again.';
            break;
          case 'passport-error':
            errorText = 'Authentication system error. Please try again later.';
            break;
          case 'access_denied':
            errorText = 'You denied access to your Google account. Please try again and grant permission.';
            break;
          default:
            errorText = `Authentication error: ${error}`;
        }

        errorMessage.textContent = errorText;
        errorMessage.style.display = 'block';
      }

      loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        try {
          const response = await fetch('/api/users/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.message || 'Login failed');
          }

          // Store user in localStorage
          localStorage.setItem('user', JSON.stringify(data.user));

          // Redirect to dashboard
          window.location.href = '/dashboard.html';
        } catch (error) {
          errorMessage.textContent = error.message;
          errorMessage.style.display = 'block';
        }
      });
    });
  </script>
</body>
</html>
