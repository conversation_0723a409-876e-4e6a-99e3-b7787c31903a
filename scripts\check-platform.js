#!/usr/bin/env node

const os = require('os');
const fs = require('fs');
const path = require('path');

function checkPlatform() {
  const platform = os.platform();
  const arch = os.arch();
  
  console.log(`🔍 Platform Check:`);
  console.log(`   Platform: ${platform}`);
  console.log(`   Architecture: ${arch}`);
  console.log(`   Node.js: ${process.version}`);
  
  // Check if better-sqlite3 is properly installed
  try {
    const Database = require('better-sqlite3');
    console.log('✅ better-sqlite3 is working correctly');
    
    // Test database creation
    const testDbPath = path.join(__dirname, '../test-db.sqlite');
    const db = new Database(testDbPath);
    db.exec('CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY)');
    db.close();
    
    // Clean up test database
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
    }
    
    console.log('✅ Database operations working correctly');
    
  } catch (error) {
    console.log('❌ better-sqlite3 error:', error.message);
    
    if (error.message.includes('invalid ELF header') || 
        error.message.includes('wrong architecture') ||
        error.message.includes('MODULE_NOT_FOUND')) {
      
      console.log('\n🔧 Platform mismatch detected!');
      console.log('This usually happens when switching between Windows and Linux.');
      console.log('\nTo fix this, run one of these commands:');
      console.log('  npm run rebuild        # Rebuild native modules');
      console.log('  npm run clean-install  # Clean install everything');
      console.log('\nOr manually:');
      console.log('  rm -rf node_modules package-lock.json');
      console.log('  npm install');
      
      process.exit(1);
    }
  }
  
  // Create platform info file
  const platformInfo = {
    platform,
    arch,
    nodeVersion: process.version,
    timestamp: new Date().toISOString(),
    betterSqlite3Working: true
  };
  
  const infoPath = path.join(__dirname, '../.platform-info.json');
  fs.writeFileSync(infoPath, JSON.stringify(platformInfo, null, 2));
  
  console.log('✅ Platform check completed successfully');
}

if (require.main === module) {
  checkPlatform();
}

module.exports = { checkPlatform };
