/* NUI Native UI Components - Dark Underwater Theme */

/* Button */
.nui-button {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.nui-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.3s ease;
}

.nui-button:hover::before {
  left: 100%;
}

.nui-button:hover {
  text-decoration: none;
  transform: translateY(-2px);
}

.nui-button:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 170, 0.25);
}

.nui-button.primary {
  color: #fff;
  background: linear-gradient(135deg, #00d4aa, #00a085);
  border-color: #00d4aa;
  box-shadow: 0 0 20px rgba(0, 212, 170, 0.2);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.nui-button.primary:hover {
  background: linear-gradient(135deg, #4ecdc4, #00d4aa);
  border-color: #4ecdc4;
  box-shadow: 0 0 30px rgba(0, 212, 170, 0.4);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.4);
}

.nui-button.secondary {
  color: #e2e8f0;
  background: linear-gradient(135deg, #26a69a, #1e8e82);
  border-color: #26a69a;
  box-shadow: 0 0 15px rgba(38, 166, 154, 0.2);
}

.nui-button.secondary:hover {
  background: linear-gradient(135deg, #4ecdc4, #26a69a);
  border-color: #4ecdc4;
  box-shadow: 0 0 25px rgba(38, 166, 154, 0.3);
  color: #fff;
}

.nui-button.small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 6px;
}

/* Input */
.nui-input {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #e2e8f0;
  background: rgba(26, 35, 50, 0.8);
  backdrop-filter: blur(10px);
  background-clip: padding-box;
  border: 1px solid rgba(0, 212, 170, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nui-input::placeholder {
  color: #718096;
}

.nui-input:focus {
  color: #e2e8f0;
  background: rgba(26, 35, 50, 0.9);
  border-color: rgba(0, 212, 170, 0.4);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 170, 0.25), 0 0 20px rgba(0, 212, 170, 0.2);
}

/* Checkbox */
input[type="checkbox"] {
  margin-right: 0.5rem;
  accent-color: #00d4aa;
}
