/* Main styles for mermantic - Dark Underwater Theme */

/* Base styles */
:root {
  /* Deep Ocean Color Palette */
  --primary-color: #00d4aa;
  --primary-dark: #00a085;
  --primary-light: #4ecdc4;
  --secondary-color: #26a69a;
  --success-color: #00ffaa;
  --danger-color: #ff6b6b;
  --warning-color: #ffd93d;

  /* Background Colors */
  --background-color: #0a0f1c;
  --background-secondary: #1a2332;
  --background-tertiary: #2d3748;
  --surface-color: rgba(26, 35, 50, 0.8);
  --surface-elevated: rgba(45, 55, 72, 0.9);

  /* Text Colors */
  --text-color: #e2e8f0;
  --text-secondary: #a0aec0;
  --text-muted: #718096;
  --text-glow: #00d4aa;

  /* Border and Effects */
  --border-color: rgba(0, 212, 170, 0.2);
  --border-glow: rgba(0, 212, 170, 0.4);
  --border-radius: 8px;
  --border-radius-large: 16px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  --box-shadow-glow: 0 0 20px rgba(0, 212, 170, 0.2);
  --box-shadow-intense: 0 0 30px rgba(0, 212, 170, 0.4);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #0a0f1c 0%, #1a2332 50%, #2d1b69 100%);
  --gradient-surface: linear-gradient(135deg, rgba(26, 35, 50, 0.9) 0%, rgba(45, 55, 72, 0.8) 100%);
  --gradient-glow: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);

  /* Animation Variables */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(0, 212, 170, 0.1), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(78, 205, 196, 0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(0, 212, 170, 0.12), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(78, 205, 196, 0.1), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(0, 212, 170, 0.08), transparent),
    var(--gradient-primary);
  background-repeat: repeat, no-repeat;
  background-size: 200px 100px, cover;
  background-attachment: fixed;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  animation: floatParticles 30s ease-in-out infinite;

  /* Flexbox layout for sticky footer */
  display: flex;
  flex-direction: column;
}

/* Underwater atmosphere effects */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(45, 27, 105, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
  animation: underwaterGlow 8s ease-in-out infinite alternate;
}

/* Subtle tentacle patterns integrated into body::before */

@keyframes underwaterGlow {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

@keyframes tentacleFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-0.5deg); }
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  position: relative;
  z-index: 1;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-color);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

h1 {
  color: var(--text-glow);
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);
  font-weight: 700;
}

h2 {
  color: var(--primary-light);
}

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
  text-shadow: 0 0 5px rgba(0, 212, 170, 0.2);
}

a:hover {
  color: var(--primary-light);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.4);
  text-decoration: none;
}

/* Navigation styles */
nav ul {
  display: flex;
  gap: 1.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

nav ul li a {
  color: var(--text-color);
  text-decoration: none;
  position: relative;
  padding: 0.5rem 0;
  transition: color var(--transition-normal), border-color var(--transition-normal);
}

/* Underline hover effect */
nav ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

nav ul li a:hover {
  color: var(--primary-light);
}

nav ul li a:hover::after {
  width: 100%;
}

/* Style for active state */
nav ul li a.active {
  color: var(--primary-color);
}

nav ul li a.active::after {
  width: 100%;
  background-color: var(--primary-color);
}

/* Header */
header {
  background: var(--gradient-surface);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--box-shadow-glow);
  padding: 1rem 0;
  position: relative;
  z-index: 100;
  margin-bottom: 30px;
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 212, 170, 0.1) 50%,
    transparent 100%);
  animation: headerGlow 4s ease-in-out infinite alternate;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--text-glow);
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.6);
  font-weight: 700;
  letter-spacing: 1px;
}

header .logo {
  max-width: 35px; 
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.6);
}

header nav ul {
  display: flex;
  list-style: none;
  gap: 1.5rem;
}

header nav ul li a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

/* Underline hover effect */
header nav ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

header nav ul li a:hover {
  color: var(--primary-light);
}

header nav ul li a:hover::after {
  width: 100%;
}

/* Style for active state */
header nav ul li a.active {
  color: var(--primary-color);
}

header nav ul li a.active::after {
  width: 100%;
  background-color: var(--primary-color);
}

@keyframes headerGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

/* Main content */
main {
  padding: 2rem 0;
  position: relative;
  z-index: 1;

  /* Flex grow to push footer to bottom */
  flex: 1;
}

/* Footer */
footer {
  background: var(--gradient-surface);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 1.5rem 0;
  position: relative;
  z-index: 1;

  /* Sticky footer - no margin-top needed with flexbox */
  margin-top: auto;
  flex-shrink: 0;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--primary-color) 50%,
    transparent 100%);
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.5);
}

/* Hero section */
.hero {
  text-align: center;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 212, 170, 0.05) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  animation: heroGlow 6s ease-in-out infinite alternate;
}

.hero h2 {
  font-size: 2.8rem;
  margin-bottom: 1.5rem;
  color: var(--text-glow);
  text-shadow: 0 0 30px rgba(0, 212, 170, 0.6);
  position: relative;
  z-index: 1;
  font-weight: 700;
}

.hero p {
  font-size: 1.3rem;
  max-width: 800px;
  margin: 0 auto 3rem;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
  line-height: 1.7;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

@keyframes heroGlow {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

/* Forms */
.auth-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 2.5rem;
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-glow);
  position: relative;
  overflow: hidden;
}

.auth-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0.5;
  z-index: -1;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  text-shadow: 0 0 5px rgba(0, 212, 170, 0.2);
}

.form-actions {
  margin-top: 2rem;
}

.auth-redirect {
  margin-top: 1.5rem;
  text-align: center;
}

/* Social login */
.social-login {
  margin-top: 2rem;
  text-align: center;
}

.social-login p {
  margin-bottom: 1rem;
  color: var(--secondary-color);
  position: relative;
}

.social-login p::before,
.social-login p::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30%;
  height: 1px;
  background-color: var(--border-color);
}

.social-login p::before {
  left: 0;
}

.social-login p::after {
  right: 0;
}

.google-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: var(--surface-elevated);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  width: 100%;
  margin-bottom: 1rem;
  transition: var(--transition-normal);
  border-radius: var(--border-radius);
  padding: 0.75rem;
  backdrop-filter: blur(10px);
}

.google-btn:hover {
  background: var(--surface-color);
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-glow);
  transform: translateY(-2px);
}

.error-message {
  background: linear-gradient(135deg, var(--danger-color), #ff5252);
  color: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: none;
  border: 1px solid rgba(255, 107, 107, 0.3);
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.2);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.success-message {
  background: linear-gradient(135deg, var(--success-color), #00e676);
  color: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: none;
  border: 1px solid rgba(0, 255, 170, 0.3);
  box-shadow: 0 0 15px rgba(0, 255, 170, 0.2);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.forgot-password {
  text-align: center;
  margin-top: 1rem;
}

.forgot-password a {
  color: var(--secondary-color);
  font-size: 0.9rem;
  text-decoration: none;
}

.forgot-password a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Dashboard */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border-radius: var(--border-radius-large);
  border: 1px solid var(--border-color);
  box-shadow: var(--box-shadow-glow);
  position: relative;
  overflow: hidden;
}

.dashboard-title-section {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.dashboard-title-section h2 {
  margin: 0;
}

.global-theme-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  background: var(--surface-elevated);
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  box-shadow: var(--box-shadow-glow);
  transition: var(--transition-normal);
}

.global-theme-selector:hover {
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-intense);
}

.global-theme-selector label {
  color: var(--text-color);
  font-weight: 500;
  white-space: nowrap;
  margin: 0;
}

.global-theme-selector select {
  background: var(--background-secondary);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.5rem;
  min-width: 150px;
  font-size: 0.9rem;
  transition: var(--transition-normal);
}

.global-theme-selector select:hover {
  border-color: var(--border-glow);
}

.global-theme-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--border-glow);
}

.global-theme-selector select option {
  background: var(--background-secondary);
  color: var(--text-color);
  padding: 0.5rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.chart-card {
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-glow);
  padding: 1.5rem;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0;
  transition: var(--transition-normal);
  z-index: -1;
}

.chart-card:hover {
  transform: translateY(-5px);
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-intense);
}

.chart-card:hover::before {
  opacity: 0.3;
}

.chart-card h4 {
  margin-bottom: 1rem;
}

.chart-preview-small {
  height: 150px;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-preview-small svg {
  max-width: 100%;
  max-height: 150px;
  display: block;
  position: relative !important;
  z-index: 1;
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

/* Editor */
.editor-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.editor-panel, .preview-panel {
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  padding: 1.5rem;
  box-shadow: var(--box-shadow-glow);
}

.mermaid-editor {
  width: 100%;
  height: 300px;
  font-family: 'Fira Code', 'Consolas', monospace;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  resize: vertical;
  background: var(--background-secondary);
  color: var(--text-color);
  transition: var(--transition-normal);
}

.mermaid-editor:focus {
  outline: none;
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-glow);
}

.mermaid-preview {
  width: 100%;
  min-height: 300px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  background: var(--background-secondary);
  overflow: auto;
  position: relative;
}

.mermaid-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0.1;
  pointer-events: none;
  border-radius: var(--border-radius);
}

.mermaid-preview .error-message {
  color: var(--danger-color);
  padding: 1rem;
  border: 1px solid var(--danger-color);
  border-radius: var(--border-radius);
  background-color: rgba(220, 53, 69, 0.1);
  font-family: system-ui, -apple-system, sans-serif;
  white-space: pre-wrap;
  margin-top: 0.5rem;
  line-height: 1.5;
  text-align: left;
}

.mermaid-preview .error-message strong {
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
}

.chart-preview-small .error-message {
  font-size: 0.8rem;
  padding: 0.5rem;
  overflow: auto;
  max-height: 140px;
}

/* Syntax indicator */
.syntax-indicator {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: normal;
  margin-left: 8px;
}

.syntax-indicator.valid {
  background-color: var(--success-color);
  color: white;
}

.syntax-indicator.invalid {
  background-color: var(--danger-color);
  color: white;
}

.syntax-indicator.checking {
  background-color: var(--secondary-color);
  color: white;
}

.syntax-indicator.warning {
  background-color: #ffc107;
  color: #212529;
}

/* Help panel */
.help-toggle {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.help-toggle:hover {
  background: var(--primary-dark-color);
}

.help-panel {
  background: var(--light-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.help-panel h5 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
}

.help-examples {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.help-example {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
}

.help-example:hover {
  background: var(--primary-color);
  color: white;
}

.help-shortcuts {
  font-size: 0.8rem;
  color: var(--secondary-color);
}

.help-unicode {
  margin-top: 10px;
  font-size: 0.8rem;
  color: var(--secondary-color);
  padding: 8px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
}

.help-shortcuts kbd {
  background: var(--dark-color);
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 0.7rem;
}

/* Fullscreen editor */
.chart-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--background-color);
  z-index: 9999;
  padding: 1rem;
  overflow-y: auto;
}

.chart-editor.fullscreen .editor-container {
  height: calc(100vh - 200px);
}

.chart-editor.fullscreen .mermaid-editor {
  height: calc(100vh - 300px);
  min-height: 400px;
}

.chart-editor.fullscreen .mermaid-preview {
  height: calc(100vh - 300px);
  min-height: 400px;
}

/* Fullscreen modal */
.modal.fullscreen {
  background-color: var(--background-color);
}

.modal.fullscreen .modal-content {
  width: 100vw;
  height: 100vh;
  max-width: none;
  margin: 0;
  border-radius: 0;
  display: flex;
  flex-direction: column;
}

.modal.fullscreen .modal-body {
  flex: 1;
  max-height: none;
  padding: 2rem;
  overflow: hidden;
}

.modal.fullscreen .view-chart-container {
  height: 100%;
  min-height: auto;
  border: none;
  padding: 0;
  background: transparent;
}

.modal.fullscreen .modal-header {
  flex-shrink: 0;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
}

.modal.fullscreen .modal-footer {
  flex-shrink: 0;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color);
}

/* Zoom controls in fullscreen modal */
.modal.fullscreen .zoom-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Folder input container */
.folder-input-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.folder-input-container input {
  flex: 1;
}

.folder-suggestions {
  min-width: 200px;
  max-width: 250px;
}

/* Chart cards with folder info */
.chart-card .chart-folder {
  font-size: 0.8rem;
  color: var(--secondary-color);
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.chart-card .chart-folder:empty {
  display: none;
}

.chart-card .chart-notes {
  font-size: 0.8rem;
  color: var(--text-color);
  margin-top: 0.5rem;
  line-height: 1.4;
  max-height: 3.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* Added standard property for compatibility */
  -webkit-box-orient: vertical;
}

.chart-card .chart-notes:empty {
  display: none;
}

/* Folder sections */
.folder-section {
  margin-bottom: 2rem;
}

.folder-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  border-bottom: 2px solid var(--primary-color);
}

.folder-icon {
  margin-right: 0.5rem;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.folder-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.folder-count {
  margin-left: auto;
  background: var(--primary-color);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Demo section */
.demo-section {
  margin: 3rem 0;
}

.features {
  margin: 3rem 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: var(--gradient-surface);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-glow);
  padding: 2rem;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0;
  transition: var(--transition-normal);
  z-index: -1;
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: var(--border-glow);
  box-shadow: var(--box-shadow-intense);
}

.feature-card:hover::before {
  opacity: 0.2;
}

.feature-card h4 {
  color: var(--primary-light);
  margin-bottom: 1rem;
}

/* Shared chart */
.shared-chart {
  max-width: 800px;
  margin: 0 auto;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--gradient-surface);
  backdrop-filter: blur(20px);
  margin: 5% auto;
  padding: 0;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  width: 80%;
  max-width: 900px;
  box-shadow: var(--box-shadow-intense);
  position: relative;
  overflow: hidden;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0.1;
  z-index: -1;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 212, 170, 0.05);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-glow);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.4);
}

.modal-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  background: rgba(0, 212, 170, 0.05);
}

.close-modal {
  color: var(--text-secondary);
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: var(--transition-fast);
}

.close-modal:hover {
  color: var(--primary-light);
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.4);
}

.view-chart-container {
  width: 100%;
  min-height: 400px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  background: var(--background-secondary);
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.view-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glow);
  opacity: 0.05;
  pointer-events: none;
  border-radius: var(--border-radius);
}

.view-chart-container svg {
  max-width: 100%;
  margin: 0 auto;
  display: block;
  position: relative !important;
  z-index: 1;
}

/* Form help */
.form-help {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-muted);
  font-style: italic;
}

/* Mermaid diagrams */
.mermaid {
  font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
}

/* Zoom controls */
.zoom-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  justify-content: flex-end;
  background: var(--surface-elevated);
  backdrop-filter: blur(15px);
  padding: 8px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  position: sticky;
  top: 10px;
  z-index: 1000;
  box-shadow: var(--box-shadow-glow);
}

.zoom-controls button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
  border-radius: var(--border-radius);
  width: 35px;
  height: 35px;
  font-size: 1.2rem;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--box-shadow-glow);
  margin: 0 2px;
  transition: var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.zoom-controls button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-fast);
}

.zoom-controls button:hover::before {
  left: 100%;
}

.zoom-controls button:hover {
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-intense);
}

.zoom-controls button:disabled {
  background: var(--background-tertiary);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Zoomable container */
.zoomable-container {
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
  transform-origin: center center;
}

.zoomable-content {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

/* Fix for Mermaid SVG positioning */
.mermaid-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Force SVG to stay within its container */
svg {
  position: relative !important;
  z-index: 1 !important;
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  overflow: visible !important;
}

/* Fix for absolute positioning in Mermaid diagrams */
.mermaid foreignObject,
.mermaid g,
.mermaid rect,
.mermaid circle,
.mermaid ellipse,
.mermaid polygon,
.mermaid path,
.mermaid line,
.mermaid text {
  position: relative !important;
}

/* More aggressive fix for Mermaid diagrams */
body svg,
.chart-preview-small svg,
.view-chart-container svg,
#chart-preview svg {
  position: static !important;
  z-index: 10 !important;
  max-width: 100% !important;
  max-height: 100% !important;
  overflow: visible !important;
}

/* Target specific chart previews */
[id^="chart-preview-"] {
  position: relative !important;
  z-index: 5 !important;
  overflow: hidden !important;
  max-height: 150px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure footer stays below content - handled by flexbox layout above */

/* Additional Lovecraftian/Organic Effects */

/* Pulsing glow animation for important elements */
@keyframes pulse {
  0%, 100% { box-shadow: 0 0 20px rgba(0, 212, 170, 0.2); }
  50% { box-shadow: 0 0 30px rgba(0, 212, 170, 0.4); }
}

/* Breathing animation for containers */
@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

/* Organic border animation */
@keyframes organicBorder {
  0%, 100% { border-radius: 8px 12px 8px 12px; }
  25% { border-radius: 12px 8px 12px 8px; }
  50% { border-radius: 8px 12px 8px 12px; }
  75% { border-radius: 12px 8px 12px 8px; }
}

/* Apply subtle animations to key elements */
.auth-form {
  animation: breathe 8s ease-in-out infinite;
}

.chart-card:hover {
  animation: pulse 2s ease-in-out infinite;
}

.feature-card {
  animation: organicBorder 6s ease-in-out infinite;
}

/* Tentacle-like decorative elements */
.hero::after {
  content: '';
  position: absolute;
  bottom: -50px;
  left: 10%;
  width: 200px;
  height: 100px;
  background: radial-gradient(ellipse 200px 100px, rgba(0, 212, 170, 0.05) 0%, transparent 70%);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  animation: tentacleFloat 10s ease-in-out infinite;
  transform-origin: center bottom;
}

/* Bioluminescent particles effect - subtle background movement only */
@keyframes floatParticles {
  0%, 100% { background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, center; }
  50% { background-position: 10px -5px, -5px 10px, 8px -3px, -3px 8px, 5px -8px, center; }
}

/* Floating particles are now integrated into the main body background */

/* Enhance scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
  box-shadow: 0 0 15px rgba(0, 212, 170, 0.5);
}

/* Responsive Design */

/* Large tablets and small desktops */
@media (max-width: 1024px) {
  .container {
    padding: 0 20px;
  }

  .charts-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .modal-content {
    width: 90%;
    margin: 3% auto;
  }
}

/* Tablets */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  /* Header navigation */
  header .container {
    flex-direction: column;
    gap: 1rem;
  }

  header nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  header nav ul li a {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  /* Hero section */
  .hero {
    padding: 2rem 0;
  }

  .hero h2 {
    font-size: 2.2rem;
  }

  .hero p {
    font-size: 1.1rem;
  }

  /* Buttons */
  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .nui-button {
    width: 100%;
    max-width: 300px;
  }

  /* Editor and grids */
  .editor-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .feature-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  /* Dashboard */
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    padding: 1rem;
  }

  .dashboard-title-section {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .global-theme-selector {
    justify-content: center;
  }

  /* Modal adjustments */
  .modal-content {
    width: 95%;
    margin: 2% auto;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .modal-footer {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  /* Chart editor adjustments */
  .mermaid-editor {
    height: 250px;
  }

  .mermaid-preview {
    min-height: 250px;
  }
}

/* Mobile phones */
@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }

  /* Header */
  header h1 {
    font-size: 1.5rem;
  }

  header nav ul {
    gap: 0.5rem;
  }

  header nav ul li a {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
  }

  /* Hero section */
  .hero {
    padding: 1.5rem 0;
  }

  .hero h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .hero p {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  /* Forms */
  .auth-form {
    padding: 1.5rem;
    margin: 1rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  /* Feature cards */
  .feature-grid {
    grid-template-columns: 1fr;
  }

  .feature-card {
    padding: 1.5rem;
  }

  /* Chart cards */
  .chart-card {
    padding: 1rem;
  }

  .chart-actions {
    flex-wrap: wrap;
    gap: 0.3rem;
  }

  .chart-actions button {
    flex: 1;
    min-width: calc(50% - 0.15rem);
  }

  /* Dashboard adjustments */
  .dashboard-header {
    padding: 0.75rem;
  }

  .dashboard-title-section h2 {
    font-size: 1.5rem;
  }

  /* Modal full-screen on mobile */
  .modal-content {
    width: 100%;
    height: 100%;
    margin: 0;
    border-radius: 0;
    max-width: none;
  }

  .modal-body {
    max-height: calc(100vh - 120px);
  }

  /* Editor adjustments */
  .editor-panel,
  .preview-panel {
    padding: 1rem;
  }

  .mermaid-editor {
    height: 200px;
    font-size: 0.9rem;
  }

  .mermaid-preview {
    min-height: 200px;
  }

  .global-theme-selector {
    flex-direction: column;
    align-items: stretch;
    width: 100%;
  }

  .global-theme-selector label {
    margin-bottom: 0.5rem;
  }

  .global-theme-selector select {
    width: 100%;
  }
}

/* Extra small devices */
@media (max-width: 320px) {
  .container {
    padding: 0 5px;
  }

  .hero h2 {
    font-size: 1.5rem;
  }

  .auth-form {
    padding: 1rem;
    margin: 0.5rem;
  }

  .chart-actions button {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }

  .modal-header h3 {
    font-size: 1.2rem;
  }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
  .hero {
    padding: 1rem 0;
  }

  .hero h2 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
  }

  .hero p {
    margin-bottom: 1rem;
  }

  .modal-content {
    margin: 1% auto;
  }

  .modal-body {
    max-height: calc(100vh - 100px);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Enhance text rendering on high DPI displays */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Print styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }

  header,
  footer,
  .modal,
  .zoom-controls,
  .chart-actions {
    display: none !important;
  }

  .chart-card,
  .feature-card {
    break-inside: avoid;
    border: 1px solid #ccc !important;
    background: white !important;
  }
}

/* Download functionality styling */
.download-group {
  display: flex;
  gap: 5px;
  align-items: center;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.preview-download-group {
  display: flex;
  gap: 5px;
}

.download-btn {
  background: var(--secondary-color) !important;
  border-color: var(--secondary-color) !important;
  color: white !important;
  font-size: 12px;
  padding: 6px 12px !important;
  transition: var(--transition-fast);
}

.download-btn:hover {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  box-shadow: var(--box-shadow-glow);
  transform: translateY(-1px);
}

.download-btn:active {
  transform: translateY(0);
}

/* Download message styling */
.download-message {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.download-message-success {
  background: linear-gradient(135deg, var(--success-color), var(--primary-color));
}

.download-message-error {
  background: linear-gradient(135deg, var(--danger-color), #ff4757);
}

/* Responsive adjustments for download buttons */
@media (max-width: 768px) {
  .download-group {
    flex-direction: column;
    gap: 3px;
  }

  .preview-download-group {
    flex-direction: column;
    gap: 3px;
  }

  .download-btn {
    font-size: 11px;
    padding: 4px 8px !important;
  }

  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* Enhanced Footer Styles */
.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem 0;
}

.footer-section {
  display: flex;
  align-items: center;
}

.footer-nav {
  display: flex;
  gap: 1.5rem;
}

.footer-nav a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition-fast);
}

.footer-nav a:hover {
  color: var(--primary-color);
  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);
}

.footer-info {
  color: var(--text-muted);
  font-size: 0.85rem;
  font-style: italic;
}

/* Responsive footer */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .footer-nav {
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .footer-nav a {
    font-size: 0.8rem;
  }

  .footer-info {
    font-size: 0.8rem;
  }
}
