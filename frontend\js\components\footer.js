// Global Footer Component
class FooterComponent {
  constructor() {
    this.currentYear = new Date().getFullYear();
  }

  getFooterHTML() {
    return `
      <footer>
        <div class="container">
          <div class="footer-content">
            <div class="footer-section">
              <p>&copy; ${this.currentYear} mermantic. All rights reserved.</p>
            </div>
            <div class="footer-section">
              <nav class="footer-nav">
                <a href="/">Home</a>
                <a href="/dashboard.html">Dashboard</a>
                <a href="https://mermaid.js.org/intro/" target="_blank" rel="noopener">Mermaid Docs</a>
              </nav>
            </div>
            <div class="footer-section">
              <div class="footer-info">
                <span>Create and share beautiful diagrams</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    `;
  }

  render(containerId = 'footer-container') {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('Footer container not found:', containerId);
      return;
    }

    container.innerHTML = this.getFooterHTML();
  }

  // Static method for simple footer
  static getSimpleFooter() {
    const currentYear = new Date().getFullYear();
    return `
      <footer>
        <div class="container">
          <p>&copy; ${currentYear} mermantic. All rights reserved.</p>
        </div>
      </footer>
    `;
  }
}

// Export for use in other scripts
window.FooterComponent = FooterComponent;
